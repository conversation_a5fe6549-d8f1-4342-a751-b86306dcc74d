# Design Guidelines

## Brand Identity

### Personal Brand Statement
"Senior backend developer with 6 years of experience building scalable systems and leading technical teams"

### Voice & Tone
- Professional but approachable
- Technical but clear
- Confident but not arrogant
- Solution-oriented and practical

## Visual Design

### Color Palette (Tailwind Configuration)
- **Primary Color:** `#3a86ff` (blue-500 equivalent)
- **Secondary Color:** `#0a2463` (blue-900 equivalent)
- **Accent Color:** `#ff9e00` (orange-400 equivalent)
- **Neutral Colors:**
  - Background: `#ffffff` (white)
  - Light background: `#f8f9fa` (gray-50)
  - Border: `#e9ecef` (gray-200)
- **Text Colors:**
  - Primary text: `#2b2d42` (gray-800)
  - Light text: `#6c757d` (gray-500)

### Typography
- **Headings:** [Sans-serif font - e.g., Montserrat, Raleway]
- **Body Text:** [Readable sans-serif - e.g., Open Sans, Roboto]
- **Code Snippets:** [Monospace font - e.g., Source Code Pro]
- **Font Sizes:**
  - H1: 32-40px
  - H2: 24-32px
  - H3: 20-24px
  - Body: 16-18px
  - Small text: 14px

### Layout Guidelines
- Clean, minimal design with ample white space
- Grid-based layout for consistency
- Maximum content width: 1200px
- Responsive breakpoints:
  - Mobile: 320-480px
  - Tablet: 481-768px
  - Desktop: 769px+

### UI Elements
- **Buttons:**
  - Primary: [Primary color]
  - Secondary: [Secondary color]
  - Text: 16px, uppercase or sentence case
  - Padding: 12px 24px
  - Border radius: 4px

- **Cards/Containers:**
  - Light background
  - Subtle shadow
  - Border radius: 4-8px
  - Padding: 24px

- **Navigation:**
  - Clean horizontal menu on desktop
  - Hamburger menu on mobile
  - Active state indicator

### Imagery Guidelines
- Professional headshot with neutral background
- Project screenshots with consistent aspect ratios
- Simple icons for skills and technologies
- Minimal use of decorative elements

## Component Library

### Hero Section
- Full-width background (color or subtle pattern)
- Centered or left-aligned content
- Clear headline and subheading
- Optional background image or gradient

### Resources Section
- Card-based or list layout
- Category filtering options
- Thumbnail previews for videos
- Brief description for each resource
- Clear external links with proper styling

### About Section
- Professional photo
- Two-column layout on desktop
- Timeline or cards for experience

### Portfolio Projects
- Consistent card layout
- Featured image/screenshot
- Brief description
- Technologies used as tags
- Call-to-action link

### Skills Display
- Grouped by category
- Visual skill level indicators
- Clean, scannable layout

### Contact Form
- Minimal required fields
- Clear labels
- Visible submit button
- Success/error states

## Accessibility Considerations
- Minimum contrast ratio: 4.5:1
- Focus states for all interactive elements
- Text alternatives for all images
- Semantic HTML structure


