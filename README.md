# NobiSite - Personal Portfolio Website

> 🚧 **Work in Progress** - This project is currently under development

## About

Personal portfolio website showcasing development projects and professional experience.

## Project Status

**Current Phase:** Planning & Design
**Expected Completion:** Middle of July 2025

### Completed
- [x] Project planning and requirements
- [x] Repository setup
- [ ] Wireframes and design mockups
- [ ] Content creation
- [ ] Development
- [ ] Testing and deployment

## Planned Features

- Professional homepage with introduction
- Portfolio showcase with project details
- About section with professional background
- Resume/CV section
- Contact form
- Responsive design for all devices

## Tech Stack

### Current Implementation (Phase 1)
- **Frontend:** HTML5, CSS3, Vanilla JavaScript
- **Styling:** CSS Custom Properties with modern features
- **Hosting:** Static file serving (ready for deployment)

### Recommended Migration (Phase 2)
- **Frontend Framework:** Astro.js with TypeScript
- **CSS Framework:** Tailwind CSS + CSS Modules
- **Hosting:** Vercel (Primary) + Netlify (Backup)
- **Content Management:** MDX with Frontmatter
- **Build Tools:** Vite (integrated with Astro)
- **Analytics:** Vercel Analytics + Google Analytics 4

### Migration Benefits
- **Performance:** 95+ Lighthouse scores with zero-JS by default
- **SEO:** Built-in optimizations and automatic sitemap generation
- **Developer Experience:** Modern tooling with hot reload and TypeScript
- **Maintainability:** Component-based architecture with utility-first CSS
- **Scalability:** Easy to add interactive features and blog content

## Getting Started

### Current Static Version
```bash
# Clone the repository
git clone https://github.com/Nobhokleng/nobi-site.git
cd nobi-site

# Serve locally (using any static server)
npx serve .
# or
python -m http.server 8000
```

### Recommended Migration to Astro
```bash
# Initialize new Astro project
npm create astro@latest nobi-site-astro
cd nobi-site-astro

# Install dependencies
npm install

# Add Tailwind CSS
npx astro add tailwind

# Add TypeScript support
npx astro add typescript

# Start development server
npm run dev
```

### Deployment
- **Current:** Ready for static hosting (GitHub Pages, Netlify, Vercel)
- **Recommended:** Automatic deployment via Vercel with GitHub integration

## Contact

Feel free to reach out if you have any questions or suggestions.

---

**Note:** This README will be updated regularly as the project progresses. Check back for updates on development progress and final features.